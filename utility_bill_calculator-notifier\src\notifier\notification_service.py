"""
Notification Service for Utility Bill Calculator
Handles sending notifications via email and SMS
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from typing import List, Dict, Any, Optional
from datetime import datetime
from loguru import logger

try:
    from twilio.rest import Client as TwilioClient
    TWILIO_AVAILABLE = True
except ImportError:
    TWILIO_AVAILABLE = False
    logger.warning("Twilio not available. SMS notifications will be disabled.")


class NotificationService:
    """Handles sending notifications to household members."""
    
    def __init__(self, notification_config: Dict[str, Any]):
        """Initialize notification service."""
        self.config = notification_config
        self.methods = notification_config.get('methods', ['email'])
        
        # Initialize email client
        self.email_config = notification_config.get('email', {})
        
        # Initialize SMS client
        self.sms_config = notification_config.get('sms', {})
        self.twilio_client = None
        
        if 'sms' in self.methods and TWILIO_AVAILABLE:
            self._init_twilio_client()
        
        logger.info(f"Notification service initialized with methods: {self.methods}")
    
    def _init_twilio_client(self):
        """Initialize Twilio client for SMS."""
        try:
            account_sid = self.sms_config.get('twilio_account_sid')
            auth_token = self.sms_config.get('twilio_auth_token')
            
            if account_sid and auth_token:
                self.twilio_client = TwilioClient(account_sid, auth_token)
                logger.info("Twilio client initialized successfully")
            else:
                logger.warning("Twilio credentials not configured")
        except Exception as e:
            logger.error(f"Error initializing Twilio client: {e}")
    
    def send_bill_notifications(self, member_splits: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Send bill notifications to all members.
        
        Args:
            member_splits: List of member split data
            
        Returns:
            Dictionary with notification results
        """
        results = {
            'total_sent': 0,
            'email_sent': 0,
            'sms_sent': 0,
            'failures': [],
            'success': True
        }
        
        for member_data in member_splits:
            member_result = self.send_member_notification(member_data)
            
            if member_result['success']:
                results['total_sent'] += 1
                if member_result.get('email_sent'):
                    results['email_sent'] += 1
                if member_result.get('sms_sent'):
                    results['sms_sent'] += 1
            else:
                results['failures'].append({
                    'member': member_data['member_name'],
                    'error': member_result.get('error')
                })
                results['success'] = False
        
        logger.info(f"Sent notifications to {results['total_sent']} members")
        return results
    
    def send_member_notification(self, member_data: Dict[str, Any]) -> Dict[str, Any]:
        """Send notification to a single member."""
        result = {
            'success': True,
            'email_sent': False,
            'sms_sent': False,
            'error': None
        }
        
        member_name = member_data['member_name']
        member_email = member_data['member_email']
        member_phone = member_data.get('member_phone')
        
        try:
            # Send email notification
            if 'email' in self.methods and member_email:
                email_result = self._send_email_notification(member_data)
                result['email_sent'] = email_result
                if not email_result:
                    result['success'] = False
            
            # Send SMS notification
            if 'sms' in self.methods and member_phone and self.twilio_client:
                sms_result = self._send_sms_notification(member_data)
                result['sms_sent'] = sms_result
                if not sms_result:
                    result['success'] = False
            
            if result['success']:
                logger.info(f"Successfully sent notifications to {member_name}")
            
        except Exception as e:
            logger.error(f"Error sending notification to {member_name}: {e}")
            result['success'] = False
            result['error'] = str(e)
        
        return result
    
    def _send_email_notification(self, member_data: Dict[str, Any]) -> bool:
        """Send email notification to a member."""
        try:
            # Create email content
            subject = "Your Monthly Utility Bill Share"
            body = self._generate_email_body(member_data)
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = self.email_config['sender_email']
            msg['To'] = member_data['member_email']
            msg['Subject'] = subject
            
            msg.attach(MIMEText(body, 'html'))
            
            # Send email
            with smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port']) as server:
                server.starttls()
                server.login(self.email_config['sender_email'], self.email_config['sender_password'])
                server.send_message(msg)
            
            logger.debug(f"Email sent to {member_data['member_email']}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {member_data['member_email']}: {e}")
            return False
    
    def _send_sms_notification(self, member_data: Dict[str, Any]) -> bool:
        """Send SMS notification to a member."""
        try:
            if not self.twilio_client:
                logger.warning("Twilio client not available")
                return False
            
            # Create SMS content
            message_body = self._generate_sms_body(member_data)
            
            # Send SMS
            message = self.twilio_client.messages.create(
                body=message_body,
                from_=self.sms_config['twilio_phone_number'],
                to=member_data['member_phone']
            )
            
            logger.debug(f"SMS sent to {member_data['member_phone']}: {message.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending SMS to {member_data['member_phone']}: {e}")
            return False
    
    def _generate_email_body(self, member_data: Dict[str, Any]) -> str:
        """Generate HTML email body."""
        member_name = member_data['member_name']
        total_amount = member_data['total_amount']
        bills = member_data['bills']
        
        html = f"""
        <html>
        <head>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .header {{ background-color: #f0f0f0; padding: 20px; border-radius: 5px; }}
                .bill-item {{ margin: 10px 0; padding: 10px; border-left: 3px solid #007bff; }}
                .total {{ font-weight: bold; font-size: 18px; color: #007bff; }}
                .footer {{ margin-top: 20px; font-size: 12px; color: #666; }}
            </style>
        </head>
        <body>
            <div class="header">
                <h2>Monthly Utility Bill Notification</h2>
                <p>Hi {member_name},</p>
                <p>Here's your share of this month's utility bills:</p>
            </div>
            
            <div class="bills">
                <h3>Bill Breakdown:</h3>
        """
        
        for bill in bills:
            due_date = ""
            if bill.get('due_date'):
                try:
                    if isinstance(bill['due_date'], str):
                        due_date = f" (Due: {bill['due_date'][:10]})"
                    else:
                        due_date = f" (Due: {bill['due_date'].strftime('%Y-%m-%d')})"
                except:
                    pass
            
            html += f"""
                <div class="bill-item">
                    <strong>{bill['bill_type'].title()} Bill:</strong> ${bill['split_amount']:.2f}
                    <br><small>Total bill: ${bill['bill_amount']:.2f}{due_date}</small>
                </div>
            """
        
        html += f"""
            </div>
            
            <div class="total">
                <p>Your Total Share: ${total_amount:.2f}</p>
            </div>
            
            <div class="footer">
                <p>This notification was generated automatically by the Utility Bill Calculator.</p>
                <p>Generated on: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            </div>
        </body>
        </html>
        """
        
        return html
    
    def _generate_sms_body(self, member_data: Dict[str, Any]) -> str:
        """Generate SMS message body."""
        member_name = member_data['member_name']
        total_amount = member_data['total_amount']
        bills = member_data['bills']
        
        message = f"Hi {member_name}! Your utility bill share: "
        
        bill_parts = []
        for bill in bills:
            bill_parts.append(f"{bill['bill_type']}: ${bill['split_amount']:.2f}")
        
        message += ", ".join(bill_parts)
        message += f". Total: ${total_amount:.2f}"
        
        return message
    
    def send_test_notification(self, test_email: str, test_phone: Optional[str] = None) -> Dict[str, Any]:
        """Send test notification to verify configuration."""
        test_data = {
            'member_name': 'Test User',
            'member_email': test_email,
            'member_phone': test_phone,
            'total_amount': 75.50,
            'bills': [
                {
                    'bill_type': 'electricity',
                    'split_amount': 45.25,
                    'bill_amount': 135.75,
                    'due_date': datetime.now()
                },
                {
                    'bill_type': 'gas',
                    'split_amount': 30.25,
                    'bill_amount': 90.75,
                    'due_date': datetime.now()
                }
            ]
        }
        
        result = self.send_member_notification(test_data)
        logger.info(f"Test notification result: {result}")
        return result
    
    def send_reminder_notification(self, member_data: Dict[str, Any], days_until_due: int) -> bool:
        """Send reminder notification for upcoming due dates."""
        try:
            if 'email' in self.methods and member_data.get('member_email'):
                subject = f"Reminder: Utility Bill Due in {days_until_due} Days"
                
                html_body = f"""
                <html>
                <body>
                    <h2>Payment Reminder</h2>
                    <p>Hi {member_data['member_name']},</p>
                    <p>This is a friendly reminder that your utility bill payment of 
                    <strong>${member_data['total_amount']:.2f}</strong> is due in {days_until_due} days.</p>
                    <p>Please make sure to submit your payment on time.</p>
                    <p>Thank you!</p>
                </body>
                </html>
                """
                
                msg = MIMEMultipart()
                msg['From'] = self.email_config['sender_email']
                msg['To'] = member_data['member_email']
                msg['Subject'] = subject
                msg.attach(MIMEText(html_body, 'html'))
                
                with smtplib.SMTP(self.email_config['smtp_server'], self.email_config['smtp_port']) as server:
                    server.starttls()
                    server.login(self.email_config['sender_email'], self.email_config['sender_password'])
                    server.send_message(msg)
                
                logger.info(f"Reminder sent to {member_data['member_name']}")
                return True
                
        except Exception as e:
            logger.error(f"Error sending reminder: {e}")
            return False
    
    def validate_configuration(self) -> Dict[str, Any]:
        """Validate notification configuration."""
        validation = {
            'email_valid': False,
            'sms_valid': False,
            'errors': [],
            'warnings': []
        }
        
        # Validate email configuration
        if 'email' in self.methods:
            email_fields = ['smtp_server', 'smtp_port', 'sender_email', 'sender_password']
            missing_fields = [f for f in email_fields if not self.email_config.get(f)]
            
            if missing_fields:
                validation['errors'].append(f"Missing email configuration: {missing_fields}")
            else:
                validation['email_valid'] = True
        
        # Validate SMS configuration
        if 'sms' in self.methods:
            if not TWILIO_AVAILABLE:
                validation['errors'].append("Twilio library not installed")
            else:
                sms_fields = ['twilio_account_sid', 'twilio_auth_token', 'twilio_phone_number']
                missing_fields = [f for f in sms_fields if not self.sms_config.get(f)]
                
                if missing_fields:
                    validation['errors'].append(f"Missing SMS configuration: {missing_fields}")
                else:
                    validation['sms_valid'] = True
        
        return validation
