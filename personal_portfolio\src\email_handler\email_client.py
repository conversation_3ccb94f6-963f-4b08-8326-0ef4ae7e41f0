"""
Email Client for Utility Bill Calculator
Handles connecting to email providers and searching for utility bills
"""

import imaplib
import email
import ssl
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from loguru import logger
from email.header import decode_header


class EmailClient:
    """Email client for connecting to various email providers and searching for bills."""
    
    def __init__(self, email_config: Dict[str, Any]):
        """Initialize email client with configuration."""
        self.config = email_config
        self.connection = None
        self.is_connected = False
        
        # Auto-configure provider settings if needed
        self._auto_configure_provider()
    
    def _auto_configure_provider(self):
        """Auto-configure email provider settings."""
        provider = self.config.get('provider', '').lower()
        
        provider_settings = {
            'gmail': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993
            },
            'outlook': {
                'imap_server': 'outlook.office365.com',
                'imap_port': 993
            },
            'yahoo': {
                'imap_server': 'imap.mail.yahoo.com',
                'imap_port': 993
            }
        }
        
        if provider in provider_settings:
            settings = provider_settings[provider]
            for key, value in settings.items():
                if key not in self.config:
                    self.config[key] = value
    
    def connect(self) -> bool:
        """Connect to the email server."""
        try:
            # Create SSL context
            context = ssl.create_default_context()
            
            # Connect to IMAP server
            server = self.config.get('imap_server')
            port = self.config.get('imap_port', 993)
            
            logger.info(f"Connecting to {server}:{port}")
            
            self.connection = imaplib.IMAP4_SSL(server, port, ssl_context=context)
            
            # Login
            username = self.config['username']
            password = self.config['password']
            
            self.connection.login(username, password)
            self.is_connected = True
            
            logger.info(f"Successfully connected to email account: {username}")
            return True
            
        except imaplib.IMAP4.error as e:
            logger.error(f"IMAP error: {e}")
            self.is_connected = False
            return False
        except Exception as e:
            logger.error(f"Error connecting to email: {e}")
            self.is_connected = False
            return False
    
    def disconnect(self):
        """Disconnect from the email server."""
        if self.connection and self.is_connected:
            try:
                self.connection.close()
                self.connection.logout()
                self.is_connected = False
                logger.info("Disconnected from email server")
            except Exception as e:
                logger.warning(f"Error disconnecting from email: {e}")
    
    def search_bills(self, bill_type: str, days_back: int = 7) -> List[Dict[str, Any]]:
        """
        Search for utility bills of a specific type.
        
        Args:
            bill_type: Type of bill ('electricity' or 'gas')
            days_back: Number of days to search back
            
        Returns:
            List of email data dictionaries
        """
        if not self.is_connected:
            raise ConnectionError("Not connected to email server")
        
        try:
            # Select inbox
            self.connection.select('INBOX')
            
            # Get search criteria for this bill type
            search_criteria = self.config['search_criteria'].get(bill_type, {})
            if not search_criteria:
                logger.warning(f"No search criteria configured for {bill_type}")
                return []
            
            # Build search query
            search_query = self._build_search_query(search_criteria, days_back)
            
            logger.info(f"Searching for {bill_type} bills with query: {search_query}")
            
            # Search for emails
            status, message_ids = self.connection.search(None, search_query)
            
            if status != 'OK':
                logger.error(f"Search failed: {status}")
                return []
            
            # Get message IDs
            message_ids = message_ids[0].split()
            logger.info(f"Found {len(message_ids)} potential {bill_type} emails")
            
            # Fetch email data
            bills = []
            for msg_id in message_ids:
                email_data = self._fetch_email_data(msg_id, bill_type)
                if email_data:
                    bills.append(email_data)
            
            logger.info(f"Successfully processed {len(bills)} {bill_type} bills")
            return bills
            
        except Exception as e:
            logger.error(f"Error searching for {bill_type} bills: {e}")
            return []
    
    def _build_search_query(self, search_criteria: Dict[str, Any], days_back: int) -> str:
        """Build IMAP search query from criteria."""
        query_parts = []
        
        # Date range
        since_date = (datetime.now() - timedelta(days=days_back)).strftime('%d-%b-%Y')
        query_parts.append(f'SINCE {since_date}')
        
        # Sender
        if 'sender' in search_criteria:
            query_parts.append(f'FROM "{search_criteria["sender"]}"')
        
        # Subject keywords (search for any of the keywords)
        if 'subject_keywords' in search_criteria:
            keywords = search_criteria['subject_keywords']
            if keywords:
                # Create OR condition for subject keywords
                subject_parts = []
                for keyword in keywords:
                    subject_parts.append(f'SUBJECT "{keyword}"')
                
                if len(subject_parts) == 1:
                    query_parts.append(subject_parts[0])
                else:
                    # IMAP doesn't support OR directly in search, so we'll filter later
                    # For now, just use the first keyword
                    query_parts.append(subject_parts[0])
        
        return ' '.join(query_parts)
    
    def _fetch_email_data(self, message_id: bytes, bill_type: str) -> Optional[Dict[str, Any]]:
        """Fetch and parse email data."""
        try:
            # Fetch email
            status, msg_data = self.connection.fetch(message_id, '(RFC822)')
            
            if status != 'OK':
                logger.warning(f"Failed to fetch email {message_id}")
                return None
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract basic information
            subject = self._decode_header(email_message['Subject'])
            sender = self._decode_header(email_message['From'])
            date_str = email_message['Date']
            
            # Parse date
            email_date = email.utils.parsedate_to_datetime(date_str)
            
            # Extract email body
            body = self._extract_email_body(email_message)
            
            # Additional filtering based on subject keywords
            search_criteria = self.config['search_criteria'].get(bill_type, {})
            keywords = search_criteria.get('subject_keywords', [])
            
            if keywords:
                subject_lower = subject.lower()
                if not any(keyword.lower() in subject_lower for keyword in keywords):
                    logger.debug(f"Email subject doesn't match keywords: {subject}")
                    return None
            
            email_data = {
                'id': message_id.decode(),
                'subject': subject,
                'sender': sender,
                'date': email_date,
                'body': body,
                'bill_type': bill_type,
                'raw_message': email_message
            }
            
            logger.debug(f"Fetched email: {subject} from {sender}")
            return email_data
            
        except Exception as e:
            logger.error(f"Error fetching email {message_id}: {e}")
            return None
    
    def _decode_header(self, header: str) -> str:
        """Decode email header."""
        if not header:
            return ""
        
        try:
            decoded_parts = decode_header(header)
            decoded_string = ""
            
            for part, encoding in decoded_parts:
                if isinstance(part, bytes):
                    if encoding:
                        decoded_string += part.decode(encoding)
                    else:
                        decoded_string += part.decode('utf-8', errors='ignore')
                else:
                    decoded_string += part
            
            return decoded_string
        except Exception as e:
            logger.warning(f"Error decoding header: {e}")
            return str(header)
    
    def _extract_email_body(self, email_message) -> str:
        """Extract text body from email message."""
        body = ""
        
        try:
            if email_message.is_multipart():
                for part in email_message.walk():
                    content_type = part.get_content_type()
                    content_disposition = str(part.get("Content-Disposition"))
                    
                    # Skip attachments
                    if "attachment" in content_disposition:
                        continue
                    
                    # Get text content
                    if content_type == "text/plain":
                        charset = part.get_content_charset() or 'utf-8'
                        body += part.get_payload(decode=True).decode(charset, errors='ignore')
                    elif content_type == "text/html" and not body:
                        # Use HTML as fallback if no plain text
                        charset = part.get_content_charset() or 'utf-8'
                        html_body = part.get_payload(decode=True).decode(charset, errors='ignore')
                        # Simple HTML to text conversion
                        import re
                        body = re.sub('<[^<]+?>', '', html_body)
            else:
                # Single part message
                charset = email_message.get_content_charset() or 'utf-8'
                body = email_message.get_payload(decode=True).decode(charset, errors='ignore')
        
        except Exception as e:
            logger.error(f"Error extracting email body: {e}")
            body = ""
        
        return body.strip()
    
    def test_connection(self) -> Dict[str, Any]:
        """Test email connection and return status."""
        result = {
            'success': False,
            'message': '',
            'details': {}
        }
        
        try:
            if self.connect():
                # Try to select inbox
                self.connection.select('INBOX')
                
                # Get inbox info
                status, messages = self.connection.search(None, 'ALL')
                total_messages = len(messages[0].split()) if messages[0] else 0
                
                result.update({
                    'success': True,
                    'message': 'Connection successful',
                    'details': {
                        'server': self.config.get('imap_server'),
                        'username': self.config.get('username'),
                        'total_messages': total_messages
                    }
                })
                
                self.disconnect()
            else:
                result['message'] = 'Failed to connect to email server'
                
        except Exception as e:
            result['message'] = f'Connection test failed: {str(e)}'
        
        return result
