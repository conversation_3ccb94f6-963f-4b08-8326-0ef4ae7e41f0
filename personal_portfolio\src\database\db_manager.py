"""
Database Manager for Utility Bill Calculator
Handles storing and retrieving bill data using SQLite
"""

import sqlite3
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from pathlib import Path
from loguru import logger


class DatabaseManager:
    """Manages database operations for utility bills."""
    
    def __init__(self, db_config: Dict[str, Any]):
        """Initialize database manager."""
        self.db_path = db_config['path']
        self.db_type = db_config.get('type', 'sqlite')
        
        # Create database directory if it doesn't exist
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
        # Initialize database
        self._init_database()
    
    def _init_database(self):
        """Initialize database tables."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create bills table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bills (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        email_id TEXT UNIQUE,
                        bill_type TEXT NOT NULL,
                        amount REAL NOT NULL,
                        sender TEXT,
                        subject TEXT,
                        email_date TIMESTAMP,
                        due_date TIMESTAMP,
                        billing_period_start TIMESTAMP,
                        billing_period_end TIMESTAMP,
                        account_number TEXT,
                        kwh_usage INTEGER,
                        gas_usage INTEGER,
                        parsed_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        parser_version TEXT,
                        raw_body TEXT,
                        is_processed BOOLEAN DEFAULT FALSE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create bill_splits table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS bill_splits (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        bill_id INTEGER,
                        member_name TEXT NOT NULL,
                        member_email TEXT,
                        member_phone TEXT,
                        split_amount REAL NOT NULL,
                        is_notified BOOLEAN DEFAULT FALSE,
                        notification_date TIMESTAMP,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (bill_id) REFERENCES bills (id)
                    )
                ''')
                
                # Create processing_log table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS processing_log (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        action TEXT NOT NULL,
                        details TEXT,
                        status TEXT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                ''')
                
                # Create indexes for better performance
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_bills_type_date ON bills (bill_type, email_date)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_bills_processed ON bills (is_processed)')
                cursor.execute('CREATE INDEX IF NOT EXISTS idx_splits_bill_id ON bill_splits (bill_id)')
                
                conn.commit()
                logger.info("Database initialized successfully")
                
        except Exception as e:
            logger.error(f"Error initializing database: {e}")
            raise
    
    def save_bill(self, bill_data: Dict[str, Any]) -> int:
        """
        Save bill data to database.
        
        Args:
            bill_data: Dictionary containing bill information
            
        Returns:
            Bill ID if successful, None otherwise
        """
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Check if bill already exists (by email_id)
                if 'email_id' in bill_data:
                    cursor.execute('SELECT id FROM bills WHERE email_id = ?', (bill_data['email_id'],))
                    existing = cursor.fetchone()
                    if existing:
                        logger.info(f"Bill already exists with email_id: {bill_data['email_id']}")
                        return existing[0]
                
                # Insert new bill
                insert_query = '''
                    INSERT INTO bills (
                        email_id, bill_type, amount, sender, subject, email_date,
                        due_date, billing_period_start, billing_period_end,
                        account_number, kwh_usage, gas_usage, parsed_date,
                        parser_version, raw_body
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                '''
                
                values = (
                    bill_data.get('email_id'),
                    bill_data.get('bill_type'),
                    bill_data.get('amount'),
                    bill_data.get('sender'),
                    bill_data.get('subject'),
                    bill_data.get('email_date'),
                    bill_data.get('due_date'),
                    bill_data.get('billing_period_start'),
                    bill_data.get('billing_period_end'),
                    bill_data.get('account_number'),
                    bill_data.get('kwh_usage'),
                    bill_data.get('gas_usage'),
                    bill_data.get('parsed_date', datetime.now()),
                    bill_data.get('parser_version'),
                    bill_data.get('raw_body')
                )
                
                cursor.execute(insert_query, values)
                bill_id = cursor.lastrowid
                
                conn.commit()
                logger.info(f"Saved {bill_data.get('bill_type')} bill with ID: {bill_id}")
                
                # Log the action
                self._log_action('save_bill', f"Saved {bill_data.get('bill_type')} bill: ${bill_data.get('amount')}", 'success')
                
                return bill_id
                
        except Exception as e:
            logger.error(f"Error saving bill: {e}")
            self._log_action('save_bill', f"Error: {str(e)}", 'error')
            return None
    
    def get_recent_bills(self, days: int = 30) -> List[Dict[str, Any]]:
        """Get bills from the last N days that haven't been processed."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row  # Enable column access by name
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                query = '''
                    SELECT * FROM bills 
                    WHERE email_date >= ? AND is_processed = FALSE
                    ORDER BY email_date DESC
                '''
                
                cursor.execute(query, (cutoff_date,))
                rows = cursor.fetchall()
                
                bills = [dict(row) for row in rows]
                logger.info(f"Retrieved {len(bills)} recent unprocessed bills")
                
                return bills
                
        except Exception as e:
            logger.error(f"Error retrieving recent bills: {e}")
            return []
    
    def get_bill_summary(self, months: int = 3) -> List[Dict[str, Any]]:
        """Get bill summary for the last N months."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=months * 30)
                
                query = '''
                    SELECT 
                        bill_type,
                        amount,
                        email_date as date,
                        due_date,
                        kwh_usage,
                        gas_usage
                    FROM bills 
                    WHERE email_date >= ?
                    ORDER BY email_date DESC
                '''
                
                cursor.execute(query, (cutoff_date,))
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error retrieving bill summary: {e}")
            return []
    
    def save_bill_splits(self, bill_id: int, splits: List[Dict[str, Any]]) -> bool:
        """Save bill split information."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Delete existing splits for this bill
                cursor.execute('DELETE FROM bill_splits WHERE bill_id = ?', (bill_id,))
                
                # Insert new splits
                insert_query = '''
                    INSERT INTO bill_splits (
                        bill_id, member_name, member_email, member_phone, split_amount
                    ) VALUES (?, ?, ?, ?, ?)
                '''
                
                for split in splits:
                    values = (
                        bill_id,
                        split.get('member_name'),
                        split.get('member_email'),
                        split.get('member_phone'),
                        split.get('split_amount')
                    )
                    cursor.execute(insert_query, values)
                
                # Mark bill as processed
                cursor.execute('UPDATE bills SET is_processed = TRUE WHERE id = ?', (bill_id,))
                
                conn.commit()
                logger.info(f"Saved {len(splits)} bill splits for bill ID: {bill_id}")
                
                return True
                
        except Exception as e:
            logger.error(f"Error saving bill splits: {e}")
            return False
    
    def get_bill_splits(self, bill_id: int) -> List[Dict[str, Any]]:
        """Get bill splits for a specific bill."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                conn.row_factory = sqlite3.Row
                cursor = conn.cursor()
                
                query = '''
                    SELECT bs.*, b.bill_type, b.amount as total_amount, b.email_date
                    FROM bill_splits bs
                    JOIN bills b ON bs.bill_id = b.id
                    WHERE bs.bill_id = ?
                '''
                
                cursor.execute(query, (bill_id,))
                rows = cursor.fetchall()
                
                return [dict(row) for row in rows]
                
        except Exception as e:
            logger.error(f"Error retrieving bill splits: {e}")
            return []
    
    def mark_notifications_sent(self, bill_id: int) -> bool:
        """Mark notifications as sent for a bill."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cursor.execute('''
                    UPDATE bill_splits 
                    SET is_notified = TRUE, notification_date = CURRENT_TIMESTAMP
                    WHERE bill_id = ?
                ''', (bill_id,))
                
                conn.commit()
                logger.info(f"Marked notifications as sent for bill ID: {bill_id}")
                
                return True
                
        except Exception as e:
            logger.error(f"Error marking notifications as sent: {e}")
            return False
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                stats = {}
                
                # Total bills
                cursor.execute('SELECT COUNT(*) FROM bills')
                stats['total_bills'] = cursor.fetchone()[0]
                
                # Bills by type
                cursor.execute('SELECT bill_type, COUNT(*) FROM bills GROUP BY bill_type')
                stats['bills_by_type'] = dict(cursor.fetchall())
                
                # Average amounts
                cursor.execute('SELECT bill_type, AVG(amount) FROM bills GROUP BY bill_type')
                stats['average_amounts'] = {k: round(v, 2) for k, v in cursor.fetchall()}
                
                # Recent activity
                cursor.execute('SELECT COUNT(*) FROM bills WHERE email_date >= ?', 
                             (datetime.now() - timedelta(days=30),))
                stats['recent_bills'] = cursor.fetchone()[0]
                
                # Processed vs unprocessed
                cursor.execute('SELECT is_processed, COUNT(*) FROM bills GROUP BY is_processed')
                processed_data = dict(cursor.fetchall())
                stats['processed_bills'] = processed_data.get(1, 0)  # TRUE = 1 in SQLite
                stats['unprocessed_bills'] = processed_data.get(0, 0)  # FALSE = 0 in SQLite
                
                return stats
                
        except Exception as e:
            logger.error(f"Error retrieving statistics: {e}")
            return {}
    
    def _log_action(self, action: str, details: str, status: str):
        """Log an action to the processing log."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO processing_log (action, details, status)
                    VALUES (?, ?, ?)
                ''', (action, details, status))
                conn.commit()
        except Exception as e:
            logger.warning(f"Error logging action: {e}")
    
    def cleanup_old_data(self, days: int = 365):
        """Clean up old data older than specified days."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                cutoff_date = datetime.now() - timedelta(days=days)
                
                # Delete old bills and their splits
                cursor.execute('DELETE FROM bill_splits WHERE bill_id IN (SELECT id FROM bills WHERE email_date < ?)', (cutoff_date,))
                cursor.execute('DELETE FROM bills WHERE email_date < ?', (cutoff_date,))
                cursor.execute('DELETE FROM processing_log WHERE timestamp < ?', (cutoff_date,))
                
                deleted_count = cursor.rowcount
                conn.commit()
                
                logger.info(f"Cleaned up {deleted_count} old records")
                return deleted_count
                
        except Exception as e:
            logger.error(f"Error cleaning up old data: {e}")
            return 0
