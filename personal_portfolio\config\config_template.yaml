# Utility Bill Calculator Configuration Template
# Copy this file to config.yaml and fill in your details

# Email Configuration
email:
  provider: "gmail"  # Options: gmail, outlook, yahoo, imap
  username: "<EMAIL>"
  password: "your_app_password"  # Use app-specific password for Gmail
  imap_server: "imap.gmail.com"  # Auto-detected for common providers
  imap_port: 993
  
  # Email search criteria
  search_criteria:
    electricity:
      sender: "<EMAIL>"  # City of Austin electricity
      subject_keywords: ["electric", "bill", "statement", "austin energy"]
    gas:
      sender: "<EMAIL>"  # Texas Gas Service
      subject_keywords: ["gas", "bill", "statement", "texas gas"]

# Household Configuration
household:
  total_members: 3  # Including yourself
  members:
    - name: "Your Name"
      email: "<EMAIL>"
      phone: "+**********"
    - name: "Roommate 1"
      email: "<EMAIL>"
      phone: "+**********"
    - name: "Roommate 2"
      email: "<EMAIL>"
      phone: "+**********"

# Notification Settings
notifications:
  methods: ["email", "sms"]  # Options: email, sms, both
  
  # Email notifications
  email:
    smtp_server: "smtp.gmail.com"
    smtp_port: 587
    sender_email: "<EMAIL>"
    sender_password: "your_app_password"
  
  # SMS notifications (using Twilio)
  sms:
    twilio_account_sid: "your_twilio_sid"
    twilio_auth_token: "your_twilio_token"
    twilio_phone_number: "+**********"

# Database Configuration
database:
  type: "sqlite"  # Currently only SQLite supported
  path: "data/utility_bills.db"

# Bill Processing Settings
processing:
  # How often to check for new bills (in hours)
  check_frequency: 24
  
  # Date range to search for bills (in days)
  search_days_back: 7
  
  # Automatic processing
  auto_process: true
  auto_notify: true

# Logging
logging:
  level: "INFO"  # DEBUG, INFO, WARNING, ERROR
  file: "logs/utility_calculator.log"
