#!/usr/bin/env python3
"""
Quick test script to verify the utility bill calculator setup
"""

import sys
import os
from pathlib import Path

def test_imports():
    """Test that all required modules can be imported."""
    print("Testing imports...")
    
    try:
        # Add src to path
        sys.path.append(str(Path(__file__).parent / "src"))
        
        from src.config_manager import ConfigManager
        from src.email_handler.email_client import EmailClient
        from src.data_extractor.bill_parser import BillParser
        from src.database.db_manager import DatabaseManager
        from src.calculator.bill_calculator import BillCalculator
        from src.notifier.notification_service import NotificationService
        
        print("✓ All core modules imported successfully")
        return True
        
    except ImportError as e:
        print(f"✗ Import error: {e}")
        return False

def test_config_template():
    """Test that configuration template exists and is valid."""
    print("Testing configuration template...")
    
    config_template = Path("config/config_template.yaml")
    if not config_template.exists():
        print("✗ Configuration template not found")
        return False
    
    try:
        import yaml
        with open(config_template, 'r') as f:
            config = yaml.safe_load(f)
        
        required_sections = ['email', 'household', 'notifications', 'database']
        for section in required_sections:
            if section not in config:
                print(f"✗ Missing required section: {section}")
                return False
        
        print("✓ Configuration template is valid")
        return True
        
    except Exception as e:
        print(f"✗ Configuration template error: {e}")
        return False

def test_directory_structure():
    """Test that all required directories exist."""
    print("Testing directory structure...")
    
    required_dirs = [
        "src",
        "src/email_handler",
        "src/data_extractor", 
        "src/database",
        "src/calculator",
        "src/notifier",
        "config",
        "tests",
        "docs"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            missing_dirs.append(dir_path)
    
    if missing_dirs:
        print(f"✗ Missing directories: {missing_dirs}")
        return False
    
    print("✓ Directory structure is correct")
    return True

def test_requirements():
    """Test that requirements.txt exists and has required packages."""
    print("Testing requirements...")
    
    requirements_file = Path("requirements.txt")
    if not requirements_file.exists():
        print("✗ requirements.txt not found")
        return False
    
    with open(requirements_file, 'r') as f:
        requirements = f.read()
    
    required_packages = [
        'click',
        'loguru',
        'pyyaml',
        'beautifulsoup4',
        'pandas'
    ]
    
    missing_packages = []
    for package in required_packages:
        if package not in requirements:
            missing_packages.append(package)
    
    if missing_packages:
        print(f"✗ Missing required packages: {missing_packages}")
        return False
    
    print("✓ Requirements file is complete")
    return True

def main():
    """Run all tests."""
    print("=== Utility Bill Calculator Setup Test ===\n")
    
    tests = [
        test_directory_structure,
        test_requirements,
        test_config_template,
        test_imports
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print(f"=== Test Results: {passed}/{total} passed ===")
    
    if passed == total:
        print("🎉 All tests passed! Your setup is ready.")
        print("\nNext steps:")
        print("1. Run: python main.py setup")
        print("2. Edit config/config.yaml with your settings")
        print("3. Run: python main.py process")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        sys.exit(1)

if __name__ == "__main__":
    main()
