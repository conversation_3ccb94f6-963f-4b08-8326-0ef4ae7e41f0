"""
<PERSON> for Utility Bill Calculator
Extracts bill amounts and details from email content
"""

import re
from datetime import datetime
from typing import Dict, Any, Optional, List
from loguru import logger


class BillParser:
    """Parses utility bill information from email content."""
    
    def __init__(self, search_criteria: Dict[str, Any]):
        """Initialize bill parser with search criteria."""
        self.search_criteria = search_criteria
        
        # Common patterns for finding bill amounts
        self.amount_patterns = [
            # Standard currency patterns
            r'\$\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # $123.45 or $1,234.56
            r'(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)\s*dollars?',  # 123.45 dollars
            r'amount\s*due[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # Amount due: $123.45
            r'total[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # Total: $123.45
            r'balance[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # Balance: $123.45
            r'pay[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',  # Pay: $123.45
        ]
        
        # Patterns for finding due dates
        self.due_date_patterns = [
            r'due\s+(?:date[:\s]*)?(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',  # due date: 12/31/2024
            r'payment\s+due[:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',  # payment due: 12/31/2024
            r'due\s+by[:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',  # due by: 12/31/2024
            r'due[:\s]*([A-Za-z]+\s+\d{1,2},?\s+\d{4})',  # due: January 15, 2024
        ]
        
        # Patterns for finding billing periods
        self.period_patterns = [
            r'billing\s+period[:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s*(?:to|through|-)\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
            r'service\s+period[:\s]*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})\s*(?:to|through|-)\s*(\d{1,2}[\/\-]\d{1,2}[\/\-]\d{2,4})',
        ]
        
        # Company-specific patterns
        self.company_patterns = {
            'austin_energy': {
                'amount_patterns': [
                    r'amount\s+due[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                    r'total\s+amount\s+due[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                ],
                'identifiers': ['austin energy', 'city of austin', 'austinenergy.com']
            },
            'texas_gas_service': {
                'amount_patterns': [
                    r'amount\s+due[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                    r'current\s+charges[:\s]*\$?\s*(\d{1,3}(?:,\d{3})*(?:\.\d{2})?)',
                ],
                'identifiers': ['texas gas service', 'texasgasservice.com', 'tgs']
            }
        }
    
    def parse_bill(self, email_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """
        Parse bill information from email data.
        
        Args:
            email_data: Dictionary containing email information
            
        Returns:
            Dictionary with parsed bill data or None if parsing failed
        """
        try:
            logger.info(f"Parsing {email_data['bill_type']} bill from {email_data['sender']}")
            
            # Extract basic information
            bill_data = {
                'email_id': email_data['id'],
                'bill_type': email_data['bill_type'],
                'sender': email_data['sender'],
                'subject': email_data['subject'],
                'email_date': email_data['date'],
                'raw_body': email_data['body']
            }
            
            # Clean and prepare text for parsing
            text = self._clean_text(email_data['body'])
            
            # Extract bill amount
            amount = self._extract_amount(text, email_data['sender'])
            if amount is None:
                logger.warning(f"Could not extract amount from {email_data['bill_type']} bill")
                return None
            
            bill_data['amount'] = amount
            
            # Extract due date
            due_date = self._extract_due_date(text)
            if due_date:
                bill_data['due_date'] = due_date
            
            # Extract billing period
            period = self._extract_billing_period(text)
            if period:
                bill_data['billing_period_start'] = period[0]
                bill_data['billing_period_end'] = period[1]
            
            # Extract additional details
            details = self._extract_additional_details(text, email_data['bill_type'])
            bill_data.update(details)
            
            # Add processing metadata
            bill_data['parsed_date'] = datetime.now()
            bill_data['parser_version'] = '1.0'
            
            logger.info(f"Successfully parsed {email_data['bill_type']} bill: ${amount}")
            return bill_data
            
        except Exception as e:
            logger.error(f"Error parsing bill: {e}")
            return None
    
    def _clean_text(self, text: str) -> str:
        """Clean and normalize text for parsing."""
        if not text:
            return ""
        
        # Convert to lowercase for pattern matching
        text = text.lower()
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text)
        
        # Remove common email artifacts
        text = re.sub(r'=\d{2}', '', text)  # Remove quoted-printable encoding
        text = re.sub(r'[^\w\s\$\.\,\-\/\:\(\)]', ' ', text)  # Keep only relevant characters
        
        return text.strip()
    
    def _extract_amount(self, text: str, sender: str) -> Optional[float]:
        """Extract bill amount from text."""
        # Try company-specific patterns first
        company = self._identify_company(sender)
        if company and company in self.company_patterns:
            patterns = self.company_patterns[company]['amount_patterns']
            for pattern in patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    amount = self._parse_amount_string(matches[0])
                    if amount and amount > 0:
                        return amount
        
        # Try general patterns
        for pattern in self.amount_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                # Filter out unrealistic amounts (too high or too low)
                for match in matches:
                    amount = self._parse_amount_string(match)
                    if amount and 10 <= amount <= 1000:  # Reasonable utility bill range
                        return amount
        
        return None
    
    def _parse_amount_string(self, amount_str: str) -> Optional[float]:
        """Parse amount string to float."""
        try:
            # Remove commas and convert to float
            amount_str = amount_str.replace(',', '')
            return float(amount_str)
        except (ValueError, TypeError):
            return None
    
    def _extract_due_date(self, text: str) -> Optional[datetime]:
        """Extract due date from text."""
        for pattern in self.due_date_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                for match in matches:
                    date = self._parse_date_string(match)
                    if date:
                        return date
        return None
    
    def _parse_date_string(self, date_str: str) -> Optional[datetime]:
        """Parse date string to datetime object."""
        date_formats = [
            '%m/%d/%Y', '%m-%d-%Y', '%m/%d/%y', '%m-%d-%y',
            '%B %d, %Y', '%b %d, %Y', '%B %d %Y', '%b %d %Y'
        ]
        
        for fmt in date_formats:
            try:
                return datetime.strptime(date_str.strip(), fmt)
            except ValueError:
                continue
        
        return None
    
    def _extract_billing_period(self, text: str) -> Optional[tuple]:
        """Extract billing period from text."""
        for pattern in self.period_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                for match in matches:
                    if len(match) == 2:
                        start_date = self._parse_date_string(match[0])
                        end_date = self._parse_date_string(match[1])
                        if start_date and end_date:
                            return (start_date, end_date)
        return None
    
    def _identify_company(self, sender: str) -> Optional[str]:
        """Identify utility company from sender email."""
        sender_lower = sender.lower()
        
        for company, config in self.company_patterns.items():
            for identifier in config['identifiers']:
                if identifier in sender_lower:
                    return company
        
        return None
    
    def _extract_additional_details(self, text: str, bill_type: str) -> Dict[str, Any]:
        """Extract additional bill details based on bill type."""
        details = {}
        
        if bill_type == 'electricity':
            # Extract kWh usage
            kwh_patterns = [
                r'(\d{1,4})\s*kwh',
                r'usage[:\s]*(\d{1,4})\s*kwh',
                r'kilowatt\s*hours?[:\s]*(\d{1,4})',
            ]
            
            for pattern in kwh_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    try:
                        details['kwh_usage'] = int(matches[0])
                        break
                    except ValueError:
                        continue
        
        elif bill_type == 'gas':
            # Extract therms or CCF usage
            gas_patterns = [
                r'(\d{1,4})\s*therms?',
                r'(\d{1,4})\s*ccf',
                r'usage[:\s]*(\d{1,4})\s*(?:therms?|ccf)',
            ]
            
            for pattern in gas_patterns:
                matches = re.findall(pattern, text, re.IGNORECASE)
                if matches:
                    try:
                        details['gas_usage'] = int(matches[0])
                        break
                    except ValueError:
                        continue
        
        # Extract account number
        account_patterns = [
            r'account\s*(?:number|#)[:\s]*(\d+)',
            r'acct[:\s]*(\d+)',
        ]
        
        for pattern in account_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            if matches:
                details['account_number'] = matches[0]
                break
        
        return details
    
    def validate_bill_data(self, bill_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate parsed bill data and return validation results."""
        validation = {
            'is_valid': True,
            'warnings': [],
            'errors': []
        }
        
        # Check required fields
        required_fields = ['amount', 'bill_type', 'sender']
        for field in required_fields:
            if field not in bill_data or bill_data[field] is None:
                validation['errors'].append(f"Missing required field: {field}")
                validation['is_valid'] = False
        
        # Validate amount
        if 'amount' in bill_data:
            amount = bill_data['amount']
            if not isinstance(amount, (int, float)) or amount <= 0:
                validation['errors'].append("Invalid amount: must be a positive number")
                validation['is_valid'] = False
            elif amount < 10 or amount > 1000:
                validation['warnings'].append(f"Unusual amount: ${amount} (outside typical range)")
        
        # Validate dates
        if 'due_date' in bill_data and bill_data['due_date']:
            due_date = bill_data['due_date']
            if due_date < datetime.now():
                validation['warnings'].append("Due date is in the past")
        
        return validation
