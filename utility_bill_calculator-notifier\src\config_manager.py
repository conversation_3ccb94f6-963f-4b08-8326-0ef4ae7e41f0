"""
Configuration Manager for Utility Bill Calculator
Handles loading and validating configuration files
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any
from loguru import logger


class ConfigManager:
    """Manages application configuration."""
    
    def __init__(self, config_path: str):
        """Initialize configuration manager."""
        self.config_path = Path(config_path)
        self.config = None
        self._load_config()
    
    def _load_config(self):
        """Load configuration from YAML file."""
        if not self.config_path.exists():
            raise FileNotFoundError(f"Configuration file not found: {self.config_path}")
        
        try:
            with open(self.config_path, 'r') as file:
                self.config = yaml.safe_load(file)
            
            # Validate configuration
            self._validate_config()
            
            # Load environment variables if they exist
            self._load_env_variables()
            
            logger.info(f"Configuration loaded from {self.config_path}")
            
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in configuration file: {e}")
        except Exception as e:
            raise RuntimeError(f"Error loading configuration: {e}")
    
    def _validate_config(self):
        """Validate required configuration fields."""
        required_sections = ['email', 'household', 'notifications', 'database']
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"Missing required configuration section: {section}")
        
        # Validate email configuration
        email_config = self.config['email']
        required_email_fields = ['provider', 'username']
        for field in required_email_fields:
            if field not in email_config:
                raise ValueError(f"Missing required email configuration: {field}")
        
        # Validate household configuration
        household_config = self.config['household']
        if 'total_members' not in household_config or household_config['total_members'] < 1:
            raise ValueError("Invalid household configuration: total_members must be >= 1")
        
        if 'members' not in household_config or len(household_config['members']) == 0:
            raise ValueError("Invalid household configuration: at least one member required")
        
        logger.info("Configuration validation passed")
    
    def _load_env_variables(self):
        """Load sensitive data from environment variables."""
        # Email password
        if 'EMAIL_PASSWORD' in os.environ:
            self.config['email']['password'] = os.environ['EMAIL_PASSWORD']
        
        # Twilio credentials
        if 'TWILIO_ACCOUNT_SID' in os.environ:
            self.config['notifications']['sms']['twilio_account_sid'] = os.environ['TWILIO_ACCOUNT_SID']
        
        if 'TWILIO_AUTH_TOKEN' in os.environ:
            self.config['notifications']['sms']['twilio_auth_token'] = os.environ['TWILIO_AUTH_TOKEN']
        
        if 'TWILIO_PHONE_NUMBER' in os.environ:
            self.config['notifications']['sms']['twilio_phone_number'] = os.environ['TWILIO_PHONE_NUMBER']
    
    def get_config(self) -> Dict[str, Any]:
        """Get the full configuration dictionary."""
        return self.config
    
    def get_section(self, section: str) -> Dict[str, Any]:
        """Get a specific configuration section."""
        if section not in self.config:
            raise KeyError(f"Configuration section not found: {section}")
        return self.config[section]
    
    def get_value(self, key_path: str, default=None):
        """
        Get a configuration value using dot notation.
        Example: get_value('email.username')
        """
        keys = key_path.split('.')
        value = self.config
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            if default is not None:
                return default
            raise KeyError(f"Configuration key not found: {key_path}")
    
    def update_config(self, updates: Dict[str, Any]):
        """Update configuration with new values."""
        def deep_update(base_dict, update_dict):
            for key, value in update_dict.items():
                if isinstance(value, dict) and key in base_dict and isinstance(base_dict[key], dict):
                    deep_update(base_dict[key], value)
                else:
                    base_dict[key] = value
        
        deep_update(self.config, updates)
        logger.info("Configuration updated")
    
    def save_config(self):
        """Save current configuration back to file."""
        try:
            with open(self.config_path, 'w') as file:
                yaml.dump(self.config, file, default_flow_style=False, indent=2)
            logger.info(f"Configuration saved to {self.config_path}")
        except Exception as e:
            raise RuntimeError(f"Error saving configuration: {e}")
    
    def get_email_providers(self) -> Dict[str, Dict[str, Any]]:
        """Get predefined email provider configurations."""
        return {
            'gmail': {
                'imap_server': 'imap.gmail.com',
                'imap_port': 993,
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'use_ssl': True
            },
            'outlook': {
                'imap_server': 'outlook.office365.com',
                'imap_port': 993,
                'smtp_server': 'smtp-mail.outlook.com',
                'smtp_port': 587,
                'use_ssl': True
            },
            'yahoo': {
                'imap_server': 'imap.mail.yahoo.com',
                'imap_port': 993,
                'smtp_server': 'smtp.mail.yahoo.com',
                'smtp_port': 587,
                'use_ssl': True
            }
        }
    
    def auto_configure_email_provider(self):
        """Auto-configure email settings based on provider."""
        provider = self.config['email']['provider'].lower()
        providers = self.get_email_providers()
        
        if provider in providers:
            provider_config = providers[provider]
            
            # Update email configuration with provider defaults
            for key, value in provider_config.items():
                if key not in self.config['email']:
                    self.config['email'][key] = value
            
            logger.info(f"Auto-configured settings for {provider}")
        else:
            logger.warning(f"Unknown email provider: {provider}. Manual configuration required.")


# Utility functions for configuration
def create_default_config(output_path: str):
    """Create a default configuration file."""
    default_config = {
        'email': {
            'provider': 'gmail',
            'username': '<EMAIL>',
            'password': 'your_app_password',
            'search_criteria': {
                'electricity': {
                    'sender': '<EMAIL>',
                    'subject_keywords': ['electric', 'bill', 'statement']
                },
                'gas': {
                    'sender': '<EMAIL>',
                    'subject_keywords': ['gas', 'bill', 'statement']
                }
            }
        },
        'household': {
            'total_members': 2,
            'members': [
                {
                    'name': 'Your Name',
                    'email': '<EMAIL>',
                    'phone': '+**********'
                },
                {
                    'name': 'Roommate',
                    'email': '<EMAIL>',
                    'phone': '+**********'
                }
            ]
        },
        'notifications': {
            'methods': ['email'],
            'email': {
                'smtp_server': 'smtp.gmail.com',
                'smtp_port': 587,
                'sender_email': '<EMAIL>',
                'sender_password': 'your_app_password'
            },
            'sms': {
                'twilio_account_sid': 'your_twilio_sid',
                'twilio_auth_token': 'your_twilio_token',
                'twilio_phone_number': '+**********'
            }
        },
        'database': {
            'type': 'sqlite',
            'path': 'data/utility_bills.db'
        },
        'processing': {
            'check_frequency': 24,
            'search_days_back': 7,
            'auto_process': True,
            'auto_notify': True
        },
        'logging': {
            'level': 'INFO',
            'file': 'logs/utility_calculator.log'
        }
    }
    
    with open(output_path, 'w') as file:
        yaml.dump(default_config, file, default_flow_style=False, indent=2)
    
    return default_config
