#!/usr/bin/env python3
"""
Setup script for Monthly Utility Bill Calculator and Notifier
"""

from setuptools import setup, find_packages

with open("README.md", "r", encoding="utf-8") as fh:
    long_description = fh.read()

with open("requirements.txt", "r", encoding="utf-8") as fh:
    requirements = [line.strip() for line in fh if line.strip() and not line.startswith("#")]

setup(
    name="monthly-utility-bill-calculator",
    version="1.0.0",
    author="<PERSON><PERSON><PERSON>",
    author_email="<EMAIL>",
    description="Automated utility bill calculator and notifier for roommates",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/DyllanSchuster/monthly_utility_bill_calculator_and_notifier",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Office/Business :: Financial",
        "Topic :: Communications :: Email",
        "Topic :: Home Automation",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    entry_points={
        "console_scripts": [
            "utility-bill-calculator=main:cli",
        ],
    },
    include_package_data=True,
    package_data={
        "": ["config/*.yaml", "*.md"],
    },
)
